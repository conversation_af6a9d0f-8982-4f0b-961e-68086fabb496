#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import time
import sys
import threading
from datetime import datetime
from typing import Optional
import websockets
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 常量配置
JSON_CASE = False
AK = "dlVFlg9CQanQiqKfP7KfwGGsqyMsVHN7"  # 从百度地图开放平台申请的AK
URI = "ws://api.map.baidu.com/websocket"

# 默认配置
DEFAULT_ENTITY_ID = "yintaizhou"
DEFAULT_CASE_FILE = "shanghai_gaosu_cruise_case.jsonl"


class BaiduMapWebSocketClient:
    """百度地图WebSocket客户端"""
    
    def __init__(self, entity_id: str, case_file: str):
        self.entity_id = entity_id
        self.case_file = case_file
        self.websocket = None
        self.msg_id = 0
        self.init_flag = False
        self.running = True
        
    def get_verify_json(self) -> str:
        """构造鉴权用的JSON字符串"""
        self.msg_id += 1
        
        verify_data = {
            "id": self.msg_id,
            "qt": "auth",
            "ver": "1.0",
            "prt": 1,
            "enc": 1,
            "ts": int(time.time()),
            "data": {
                "ak": AK,
                "entity_id": self.entity_id
            }
        }
        
        message = json.dumps(verify_data)
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"{current_time} send {message}")
        
        return message
    
    async def on_message(self, message: str):
        """处理接收到的消息"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"{current_time} receive {message}")
        
        try:
            data = json.loads(message)
            await self.process_message(data)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON message: {e}")
    
    async def process_message(self, data: dict):
        """解析返回的数据"""
        # 处理鉴权结果
        if data.get("qt") == "auth" and data.get("data", {}).get("status") == 0:
            logger.info("Authentication successful")
            self.init_flag = True
            return
        
        # 处理巡航消息
        if data.get("qt") == "horizon":
            logger.info("Received horizon message")
            # 可以在这里添加具体的巡航消息处理逻辑
    
    async def send_message(self, message: str):
        """发送消息到服务端"""
        if not self.init_flag:
            logger.warning("Connection not established, ignore this message")
            return
        
        if self.websocket is None:
            logger.warning("WebSocket connection is None")
            return
        
        try:
            await self.websocket.send(message)
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
    
    async def connect_and_authenticate(self):
        """连接并进行身份验证"""
        try:
            logger.info(f"Connecting to {URI}")
            async with websockets.connect(URI) as websocket:
                self.websocket = websocket
                logger.info("WebSocket connection established")
                
                # 发送鉴权请求
                verify_message = self.get_verify_json()
                await websocket.send(verify_message)
                
                # 监听消息
                async for message in websocket:
                    await self.on_message(message)
                    
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
        except Exception as e:
            logger.error(f"WebSocket connection error: {e}")
        finally:
            self.init_flag = False
            self.websocket = None
    
    async def replay_worker(self):
        """回放GPS数据的工作线程"""
        logger.info("Starting replay worker")

        # 等待连接建立
        while not self.init_flag and self.running:
            logger.info("Connection not established, waiting 1s...")
            await asyncio.sleep(1)

        if not self.running:
            return

        try:
            with open(self.case_file, 'r', encoding='utf-8') as file:
                line_count = 0
                for line in file:
                    if not self.running:
                        break

                    line = line.strip()
                    if line:
                        line_count += 1
                        await self.send_message(line)
                        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        logger.info(f"{current_time} send line {line_count}: {line[:100]}...")

                        # 建议按1秒间隔上报数据
                        await asyncio.sleep(1)

                logger.info(f"Replay completed. Total {line_count} messages sent.")

        except FileNotFoundError:
            logger.error(f"Case file not found: {self.case_file}")
        except Exception as e:
            logger.error(f"Error in replay worker: {e}")
    
    async def client_worker(self):
        """保持WebSocket连接的工作协程"""
        while self.running:
            try:
                await self.connect_and_authenticate()
            except Exception as e:
                logger.error(f"Client worker error: {e}")
            
            if self.running:
                logger.info("Retry connect to baidu map service, wait 5 sec")
                await asyncio.sleep(5)
    
    async def start(self):
        """启动客户端"""
        logger.info(f"Starting BaiduMap WebSocket Client")
        logger.info(f"Entity ID: {self.entity_id}")
        logger.info(f"Case file: {self.case_file}")
        
        # 创建并启动任务
        client_task = asyncio.create_task(self.client_worker())
        replay_task = asyncio.create_task(self.replay_worker())
        
        try:
            # 等待任务完成
            await asyncio.gather(client_task, replay_task)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal, shutting down...")
            self.running = False
            client_task.cancel()
            replay_task.cancel()
            
            try:
                await asyncio.gather(client_task, replay_task, return_exceptions=True)
            except Exception:
                pass


def main():
    """主函数"""
    # 解析命令行参数
    entity_id = DEFAULT_ENTITY_ID
    case_file = DEFAULT_CASE_FILE
    
    if len(sys.argv) > 1 and sys.argv[1]:
        entity_id = sys.argv[1]
    
    if len(sys.argv) > 2 and sys.argv[2]:
        case_file = sys.argv[2]
    
    # 创建客户端并启动
    client = BaiduMapWebSocketClient(entity_id, case_file)
    
    try:
        asyncio.run(client.start())
    except KeyboardInterrupt:
        logger.info("Program interrupted by user")
    except Exception as e:
        logger.error(f"Program error: {e}")


if __name__ == "__main__":
    main()
