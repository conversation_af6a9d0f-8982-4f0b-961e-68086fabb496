# 百度地图API接口说明文档

## 接口概述
该API用于获取路线的电池电量消耗（SOC）预测数据，基于路线ID、路段信息等参数计算电动车在行驶过程中的电量变化。

## 请求参数

### 请求方式
POST

### 请求参数列表

| 参数名 | 参数类型 | 参数含义 | 是否必须 | 备注 |
|--------|----------|----------|----------|------|
| session_id | String | 会话标识符 | 是 | 包含编码器信息和位置坐标的JSON字符串，格式：{"codr":"编码","loc":"位置"}@序号 |
| mrsl | String | 地图渲染参数 | 是 | 包含图层、权重、页面等渲染配置信息的字符串 |
| route_id | String | 路线唯一标识 | 是 | 用于标识特定的导航路线 |
| linkids | Array[Number] | 路段ID数组 | 是 | 路线中各个路段的唯一标识符列表 |
| linklength | Array[Number] | 路段长度数组 | 是 | 各路段的长度，单位：米，与linkids数组一一对应 |
| linkspeed | Array[Number] | 路段速度数组 | 是 | 各路段的行驶速度，单位：km/h，与linkids数组一一对应 |
| travelTime | Array[Number] | 路段行驶时间数组 | 是 | 各路段的预计行驶时间，单位：秒，与linkids数组一一对应 |

### 请求示例

```json
{
    "session_id": "{\"codr\":\"5F3FD1626A81E0E46BFFD173EE962B15|0_31.260950,121.629770_31.225320,121.640670_81\",\"loc\":\"mirror\"}@641",
    "mrsl": "\"g\":\"0_1\",\"w\":\"AAAA\",\"p\":\"1\",\"label\":\"8\",\"s\":\"0\",\"seq\":\"0\"",
    "route_id": "17089338650093",
    "linkids": [
        15606470330,
        16289313980,
        16289313020,
        16289311070
    ],
    "linklength": [
        118,
        337,
        17,
        21
    ],
    "linkspeed": [
        6,
        10,
        9,
        8
    ],
    "travelTime": [
        20,
        34,
        2,
        3
    ]
}
```

## 响应参数

### 响应参数列表

| 参数名 | 参数类型 | 参数含义 | 备注 |
|--------|----------|----------|------|
| resultCode | Number | 响应状态码 | 200表示成功，其他值表示失败 |
| message | String | 响应消息 | 描述请求处理结果 |
| elaspedMilliseconds | Number | 请求处理耗时 | 单位：毫秒 |
| data | Object | 响应数据对象 | 包含具体的业务数据 |
| data.soc | Array[Number] | 电池电量状态数组 | 路线行驶过程中各时间点的电池剩余电量比例（0-1之间的小数） |

### 响应示例

```json
{
    "resultCode": 200,
    "message": "success",
    "elaspedMilliseconds": 100,
    "data": {
        "soc": [
            0.5,
            0.*********,
            0.*********,
            0.*********,
            0.49992911,
            0.*********,
            0.*********,
            0.*********,
            0.49985822,
            0.499840498,
            0.499822775,
            0.499805052,
            0.49978733,
            0.499769607,
            0.499735743,
            0.499656448,
            0.499528976,
            0.499369159,
            0.499163451,
            0.499252506,
            0.499066735,
            0.498985444,
            0.498946138,
            0.498918807,
            0.498861494,
            0.498753448,
            0.498613526,
            0.498428858,
            0.498203755,
            0.498052042,
            0.498052311,
            0.497884009,
            0.497866283,
            0.497848557,
            0.49776672,
            0.497702178,
            0.497679967,
            0.497719212,
            0.497836225,
            0.497988203,
            0.498118591,
            0.498229883,
            0.498342486,
            0.498460649,
            0.498555591,
            0.498593212,
            0.498591482,
            0.498579751,
            0.498571568,
            0.498563207,
            0.49854846,
            0.498535378,
            0.498540586,
            0.49856183,
            0.*********,
            0.*********,
            0.*********,
            0.*********,
            0.*********
        ]
    }
}
```

## 状态码说明

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求处理成功，返回正常数据 |
| 400 | 请求错误 | 请求参数格式错误或缺少必要参数 |
| 500 | 服务器错误 | 服务器内部处理异常 |

## 注意事项

1. **数组对应关系**：linkids、linklength、linkspeed、travelTime四个数组必须长度一致，且元素一一对应
2. **SOC数据**：响应中的soc数组表示电池电量的变化趋势，数值范围在0-1之间，1表示满电，0表示无电
3. **坐标格式**：session_id中的坐标采用经纬度格式（longitude,latitude）
4. **时间单位**：travelTime使用秒为单位，elaspedMilliseconds使用毫秒为单位
5. **数据精度**：SOC数据保留小数点后9位，确保计算精度

## 使用场景

该API主要用于：
- 电动车导航路线规划
- 电池电量消耗预测
- 充电站推荐决策
- 行程能耗分析
